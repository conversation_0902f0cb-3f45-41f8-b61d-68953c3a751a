package com.jurassic.myhealth.service.healthcare.data.gateway

import com.jurassic.myhealth.service.healthcare.data.client.luxmed.LuxMedClient
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.LuxMedEventTypeRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineEventRemote
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineRequest
import com.jurassic.myhealth.service.healthcare.data.gateway.mapper.LuxMedEventMapper
import com.jurassic.myhealth.service.healthcare.data.gateway.mapper.LuxMedLabResultMapper
import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedGateway
import com.jurassic.myhealth.service.healthcare.domain.gateway.LuxmedMedicalData
import com.jurassic.myhealth.service.healthcare.domain.model.LabResult
import com.jurassic.myhealth.service.healthcare.domain.model.UserId
import mu.KotlinLogging
import org.springframework.stereotype.Component

private val logger = KotlinLogging.logger {}

/**
 * Implementation of LuxmedMedGateway using LuxMed API client
 */
@Component
class LuxmedMedGatewayImpl(
    private val luxMedClient: LuxMedClient,
    private val eventMapper: LuxMedEventMapper,
    private val labResultMapper: LuxMedLabResultMapper
) : LuxmedMedGateway {

    override suspend fun fetchAllResults(userId: UserId, luxmedAuthentication: LuxMedAuthentication): Result<LuxmedMedicalData> {
        return try {
            logger.info { "Fetching medical data from LuxMed for user: $userId" }

            // Fetch timeline events for medical data (lab tests, visits, examinations)
            val medicalEventTypes = listOf(
                LuxMedEventTypeRemote.LABORATORY_TEST,
                LuxMedEventTypeRemote.VISIT,
                LuxMedEventTypeRemote.TELEMEDICINE_VISIT,
                LuxMedEventTypeRemote.PHONE_CONSULTATION,
                LuxMedEventTypeRemote.EXAMINATION
            )

            val timelineRequest = TimelineRequest.withEventTypes(medicalEventTypes)
            val timelineResponse = luxMedClient.getTimelineEvents(luxmedAuthentication, timelineRequest)

            logger.debug { "Fetched ${timelineResponse.events.size} events from LuxMed" }

            // Separate lab events from other events
            val labEvents = timelineResponse.events.filter {
                it.eventType == LuxMedEventTypeRemote.LABORATORY_TEST.id
            }
            val otherEvents = timelineResponse.events.filter {
                it.eventType != LuxMedEventTypeRemote.LABORATORY_TEST.id
            }

            // Fetch detailed lab results with TestResultRemote data
            val labResults = fetchDetailedLabResults(userId, luxmedAuthentication, labEvents)

            // Map other events (visits) using the existing mapper
            val (_, visits) = eventMapper.mapEvents(userId, otherEvents)

            logger.info {
                "Mapped LuxMed data for user $userId: ${labResults.size} lab results, ${visits.size} visits"
            }

            val medicalData = LuxmedMedicalData(
                labResults = labResults,
                visits = visits
            )

            Result.success(medicalData)

        } catch (e: Exception) {
            logger.error(e) { "Failed to fetch medical data from LuxMed for user: $userId" }
            Result.failure(e)
        }
    }

    /**
     * Fetch detailed lab results with TestResultRemote data
     */
    private suspend fun fetchDetailedLabResults(
        userId: UserId,
        luxmedAuthentication: LuxMedAuthentication,
        labEvents: List<TimelineEventRemote>
    ): List<LabResult> {
        val labResults = mutableListOf<LabResult>()

        labEvents.forEach { event ->
            try {
                logger.debug { "Fetching detailed lab result for event ${event.eventId}" }

                // Create request for event details
                val eventDetailsRequest = EventDetailsRequest.forEvent(
                    eventId = event.eventId,
                    eventType = LuxMedEventTypeRemote.LABORATORY_TEST,
                    assignedFromReservationId = event.assignedFromReservationId ?: false
                )

                // Fetch detailed event information
                val eventDetailsResponse = luxMedClient.getEventDetails(luxmedAuthentication, eventDetailsRequest)

                // Map to domain lab result using the lab result mapper
                val labResult = labResultMapper.mapLabResultFromEventDetails(userId, eventDetailsResponse)

                if (labResult != null) {
                    labResults.add(labResult)
                    logger.debug { "Successfully mapped lab result for event ${event.eventId}" }
                } else {
                    logger.warn { "Failed to map lab result for event ${event.eventId}" }
                }

            } catch (e: Exception) {
                logger.error(e) { "Failed to fetch detailed lab result for event ${event.eventId}" }
                // Continue with other events even if one fails
            }
        }

        return labResults
    }
}
