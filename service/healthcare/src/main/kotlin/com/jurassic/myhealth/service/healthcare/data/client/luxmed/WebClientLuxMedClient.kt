package com.jurassic.myhealth.service.healthcare.data.client.luxmed

import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.EventDetailsResponse
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineRequest
import com.jurassic.myhealth.service.healthcare.data.client.luxmed.model.timeline.TimelineResponse
import com.jurassic.myhealth.service.healthcare.domain.model.luxmed.LuxMedAuthentication
import org.springframework.http.HttpHeaders
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.awaitBody
import org.springframework.web.util.UriComponentsBuilder

/**
 * WebClient implementation of the LuxMed API client
 *
 * Implements LuxMed Patient Portal API authentication requirements based on analysis:
 * - Essential authentication cookies (ASP.NET_SessionId, <PERSON><PERSON>oken, RefreshToken, UserAdditionalInfo)
 * - CSRF protection with XSRF-TOKEN
 * - Browser-like headers for authenticity
 * - Proper JWT token handling in authorization-token header
 */
class WebClientLuxMedClient(
    private val webClient: WebClient
) : LuxMedClient {

    companion object {
        private const val BASE_URL = "https://portalpacjenta.luxmed.pl/PatientPortal/NewPortal"
        private const val TIMELINE_EVENTS_PATH = "/Timeline/getevents"
        private const val EVENT_DETAILS_PATH = "/Timeline/geteventdetails"
        private const val AUTH_HEADER = "authorization-token"
        private const val REFERER_URL = "https://portalpacjenta.luxmed.pl/PatientPortal/NewPortal/Page/Timeline"
        private const val USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 OPR/118.0.0.0"
    }



    /**
     * Configures WebClient request with all required LuxMed authentication headers and cookies
     */
    private fun WebClient.RequestHeadersSpec<*>.configureLuxMedAuth(authentication: LuxMedAuthentication): WebClient.RequestHeadersSpec<*> {
        return this
            // Essential authentication header
            .header(AUTH_HEADER, "Bearer ${authentication.jwtToken}")

            // Essential cookies for authentication
            .cookie("ASP.NET_SessionId", authentication.aspNetSessionId)
            .cookie("LXToken", authentication.lxToken)
            .cookie("RefreshToken", authentication.refreshToken)
            .cookie("UserAdditionalInfo", authentication.userAdditionalInfo)
            .cookie("XSRF-TOKEN", authentication.xsrfToken)

            // Optional security cookies
            .apply {
                authentication.incapsulaSessionId?.let { cookie("incap_ses_", it) }
                authentication.deviceId?.let { cookie("DeviceId", it) }
            }

            // Browser-like headers for authenticity
            .header(HttpHeaders.ACCEPT, "application/json, text/plain, */*")
            .header(HttpHeaders.ACCEPT_LANGUAGE, "pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7")
            .header(HttpHeaders.ACCEPT_ENCODING, "gzip, deflate, br")
            .header(HttpHeaders.CACHE_CONTROL, "no-cache")
            .header("pragma", "no-cache")
            .header("priority", "u=1, i")
            .header(HttpHeaders.REFERER, REFERER_URL)
            .header("sec-ch-ua", "\"Not(A:Brand\";v=\"99\", \"Opera\";v=\"118\", \"Chromium\";v=\"133\"")
            .header("sec-ch-ua-mobile", "?0")
            .header("sec-ch-ua-platform", "\"macOS\"")
            .header("sec-fetch-dest", "empty")
            .header("sec-fetch-mode", "cors")
            .header("sec-fetch-site", "same-origin")
            .header(HttpHeaders.USER_AGENT, USER_AGENT)
            .header("x-requested-with", "XMLHttpRequest")
            .header("X-XSRF-TOKEN", authentication.xsrfToken) // CSRF protection
    }

    /**
     * Get timeline events
     * @param authentication LuxMed authentication context with all required components
     * @param request Timeline request parameters
     * @return Timeline response with events
     */
    override suspend fun getTimelineEvents(authentication: LuxMedAuthentication, request: TimelineRequest): TimelineResponse {
        val uri = UriComponentsBuilder.fromUriString(BASE_URL + TIMELINE_EVENTS_PATH)
            // LuxMed API expects all parameters to be present, even if null
            .queryParam("eventTypeIds", request.eventTypeIds ?: "null")
            .queryParam("dateFrom", request.dateFrom ?: "null")
            .queryParam("dateTo", request.dateTo ?: "null")
            .queryParam("beforeDate", request.beforeDate ?: "null")
            .build()
            .toUri()

        return webClient.get()
            .uri(uri)
            .configureLuxMedAuth(authentication)
            .retrieve()
            .awaitBody()
    }

    /**
     * Get event details
     * @param authentication LuxMed authentication context with all required components
     * @param request Event details request parameters
     * @return Event details response
     */
    override suspend fun getEventDetails(authentication: LuxMedAuthentication, request: EventDetailsRequest): EventDetailsResponse {
        val uri = UriComponentsBuilder.fromUriString(BASE_URL + EVENT_DETAILS_PATH)
            .queryParam("eventId", request.eventId)
            .queryParam("eventType", request.eventType)
            .queryParam("assignedFromReservationId", request.assignedFromReservationId)
            .build()
            .toUri()

        return webClient.get()
            .uri(uri)
            .configureLuxMedAuth(authentication)
            .retrieve()
            .awaitBody()
    }
}
